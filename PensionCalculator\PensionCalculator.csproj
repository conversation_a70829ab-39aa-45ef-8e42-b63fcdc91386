﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishSingleFile>true</PublishSingleFile>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
    <PublishReadyToRun>false</PublishReadyToRun>
    <TieredCompilation>false</TieredCompilation>
    <AssemblyTitle>نظام حسابات المعاشات</AssemblyTitle>
    <AssemblyDescription>تطبيق حسابات المعاشات - إعداد أ/ أحمد ابراهيم</AssemblyDescription>
    <AssemblyCompany>أحمد ابراهيم</AssemblyCompany>
    <AssemblyProduct>نظام حسابات المعاشات</AssemblyProduct>
    <AssemblyCopyright>© 2024 أحمد ابراهيم</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

</Project>