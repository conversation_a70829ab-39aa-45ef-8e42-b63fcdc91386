using System;
using System.Windows.Forms;
using System.Drawing;

namespace PensionCalculator
{
    public partial class Form1 : Form
    {
        private TabControl tabControl;
        private TabPage tabDeductions;
        private TabPage tabHealthInsurance;
        private TabPage tabReversedContributions;
        private TabPage tabDelayInterest;

        // تبويب حساب الاستقطاعات
        private TextBox txtAmount;
        private Button btnCalculate;
        private TextBox txtOriginalAmount;
        private TextBox txt12Percent;
        private TextBox txt9Percent;
        private TextBox txt3Percent;
        private TextBox txt1Percent1;
        private TextBox txt1Percent2;
        private TextBox txt1Percent3;
        private TextBox txt1Percent4;
        private TextBox txt025Percent;
        private TextBox txtTotalDeduction;
        private TextBox txtNetAmount;

        // تبويب حساب التأمين الصحي
        private TextBox txtHealthAmount;
        private Button btnCalculateHealth;
        private TextBox txtHealth3Percent;
        private TextBox txtHealth1Percent;
        private TextBox txtHealthTotal;

        // تبويب حساب الاشتراكات المعكوسة
        private TextBox txtReversedAmount;
        private Button btnCalculateReversed;
        private TextBox txtReversedResult;

        // تبويب حساب فوائد التأخير
        private TextBox txtInstallmentValue;
        private TextBox txtDelayMonths;
        private TextBox txtInterestRate;
        private Button btnCalculateDelayInterest;
        private TextBox txtDelayInterestResult;

        public Form1()
        {
            InitializeComponent();
            InitializeCustomComponents();
        }

        private void InitializeCustomComponents()
        {
            this.Text = "نظام حسابات المعاشات";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 10F);

            // العنوان
            Label lblTitle = new Label();
            lblTitle.Text = "نظام حسابات المعاشات";
            lblTitle.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            lblTitle.AutoSize = true;
            lblTitle.Location = new Point(280, 20);
            this.Controls.Add(lblTitle);

            // إنشاء التبويبات
            tabControl = new TabControl();
            tabControl.Location = new Point(20, 60);
            tabControl.Size = new Size(750, 480);

            CreateDeductionsTab();
            CreateHealthInsuranceTab();
            CreateReversedContributionsTab();
            CreateDelayInterestTab();

            // إضافة التبويبات إلى التحكم
            tabControl.Controls.Add(tabDeductions);
            tabControl.Controls.Add(tabHealthInsurance);
            tabControl.Controls.Add(tabReversedContributions);
            tabControl.Controls.Add(tabDelayInterest);

            this.Controls.Add(tabControl);
        }

        private void CreateDelayInterestTab()
        {
            // تبويب حساب غرامة الأقساط
            tabDelayInterest = new TabPage("حساب غرامة الاقساط");

            Label lblInstallmentValue = new Label();
            lblInstallmentValue.Text = "قيمة القسط:";
            lblInstallmentValue.Location = new Point(600, 20);
            lblInstallmentValue.AutoSize = true;

            txtInstallmentValue = new TextBox();
            txtInstallmentValue.Location = new Point(400, 20);
            txtInstallmentValue.Size = new Size(180, 25);

            Label lblDelayMonths = new Label();
            lblDelayMonths.Text = "عدد شهور التأخير:";
            lblDelayMonths.Location = new Point(600, 60);
            lblDelayMonths.AutoSize = true;

            txtDelayMonths = new TextBox();
            txtDelayMonths.Location = new Point(400, 60);
            txtDelayMonths.Size = new Size(180, 25);

            Label lblInterestRate = new Label();
            lblInterestRate.Text = "نسبة الفائدة (%):";
            lblInterestRate.Location = new Point(600, 100);
            lblInterestRate.AutoSize = true;

            txtInterestRate = new TextBox();
            txtInterestRate.Location = new Point(400, 100);
            txtInterestRate.Size = new Size(180, 25);
            txtInterestRate.Text = "15.50"; // القيمة الافتراضية

            btnCalculateDelayInterest = new Button();
            btnCalculateDelayInterest.Text = "حساب غرامة تأخير الأقساط";
            btnCalculateDelayInterest.Location = new Point(400, 140);
            btnCalculateDelayInterest.Size = new Size(180, 35);
            btnCalculateDelayInterest.BackColor = Color.LightBlue;
            btnCalculateDelayInterest.Click += new EventHandler(BtnCalculateDelayInterest_Click);

            // زر التلميحات
            Button btnHelp = new Button();
            btnHelp.Text = "تلميحات";
            btnHelp.Location = new Point(600, 140);
            btnHelp.Size = new Size(80, 35);
            btnHelp.BackColor = Color.LightGreen;
            btnHelp.Click += new EventHandler(BtnHelp_Click);

            Label lblDelayInterestResult = new Label();
            lblDelayInterestResult.Text = "إجمالي غرامة التأخير:";
            lblDelayInterestResult.Location = new Point(600, 190);
            lblDelayInterestResult.AutoSize = true;
            lblDelayInterestResult.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

            txtDelayInterestResult = new TextBox();
            txtDelayInterestResult.Location = new Point(400, 190);
            txtDelayInterestResult.Size = new Size(180, 25);
            txtDelayInterestResult.ReadOnly = true;
            txtDelayInterestResult.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            txtDelayInterestResult.BackColor = Color.LightYellow;

            // ملاحظة قانونية
            Label lblLegalNote = new Label();
            lblLegalNote.Text = "ملاحظة: إجمالي الغرامة لا يتجاوز أصل المبلغ طبقاً لنص المادة 121 من قانون التأمينات";
            lblLegalNote.Location = new Point(50, 240);
            lblLegalNote.Size = new Size(650, 30);
            lblLegalNote.Font = new Font("Segoe UI", 9F, FontStyle.Italic);
            lblLegalNote.ForeColor = Color.DarkRed;

            tabDelayInterest.Controls.Add(lblInstallmentValue);
            tabDelayInterest.Controls.Add(txtInstallmentValue);
            tabDelayInterest.Controls.Add(lblDelayMonths);
            tabDelayInterest.Controls.Add(txtDelayMonths);
            tabDelayInterest.Controls.Add(lblInterestRate);
            tabDelayInterest.Controls.Add(txtInterestRate);
            tabDelayInterest.Controls.Add(btnCalculateDelayInterest);
            tabDelayInterest.Controls.Add(btnHelp);
            tabDelayInterest.Controls.Add(lblDelayInterestResult);
            tabDelayInterest.Controls.Add(txtDelayInterestResult);
            tabDelayInterest.Controls.Add(lblLegalNote);
        }

        // حساب غرامة تأخير الأقساط
        private void BtnCalculateDelayInterest_Click(object sender, EventArgs e)
        {
            try
            {
                decimal installmentValue, delayMonths, interestRate;

                if (decimal.TryParse(txtInstallmentValue.Text, out installmentValue) &&
                    decimal.TryParse(txtDelayMonths.Text, out delayMonths) &&
                    decimal.TryParse(txtInterestRate.Text, out interestRate))
                {
                    // تطبيق المعادلة: قيمة القسط × عدد الشهور × نسبة الفائدة ÷ 12 × عدد الشهور ÷ 2
                    // المعادلة المبسطة: قيمة القسط × (عدد الشهور)² × نسبة الفائدة ÷ 12 ÷ 2
                    decimal delayInterest = Math.Round(
                        installmentValue * delayMonths * (interestRate / 100m) / 12m * delayMonths / 2m,
                        2
                    );

                    // تطبيق المادة 121: الغرامة لا تتجاوز أصل المبلغ
                    if (delayInterest > installmentValue)
                    {
                        delayInterest = installmentValue;
                        txtDelayInterestResult.Text = delayInterest.ToString("N2") + " جنيه (محدود بأصل المبلغ)";
                    }
                    else
                    {
                        txtDelayInterestResult.Text = delayInterest.ToString("N2") + " جنيه";
                    }
                }
                else
                {
                    MessageBox.Show("الرجاء إدخال قيم صحيحة في جميع الحقول", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("حدث خطأ: {0}", ex.Message), "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CreateDeductionsTab()
        {
            // تبويب حساب الاستقطاعات
            tabDeductions = new TabPage("حساب الاستقطاعات");

            Label lblAmount = new Label();
            lblAmount.Text = "المبلغ:";
            lblAmount.Location = new Point(600, 20);
            lblAmount.AutoSize = true;

            txtAmount = new TextBox();
            txtAmount.Location = new Point(400, 20);
            txtAmount.Size = new Size(180, 25);

            btnCalculate = new Button();
            btnCalculate.Text = "حساب";
            btnCalculate.Location = new Point(400, 50);
            btnCalculate.Size = new Size(100, 30);
            btnCalculate.Click += new EventHandler(BtnCalculate_Click);

            // النتائج
            Label lblOriginalAmount = new Label();
            lblOriginalAmount.Text = "المبلغ الأساسي:";
            lblOriginalAmount.Location = new Point(600, 90);
            lblOriginalAmount.AutoSize = true;

            txtOriginalAmount = new TextBox();
            txtOriginalAmount.Location = new Point(400, 90);
            txtOriginalAmount.Size = new Size(180, 25);
            txtOriginalAmount.ReadOnly = true;

            Label lbl12Percent = new Label();
            lbl12Percent.Text = "استقطاع 12%:";
            lbl12Percent.Location = new Point(600, 120);
            lbl12Percent.AutoSize = true;

            txt12Percent = new TextBox();
            txt12Percent.Location = new Point(400, 120);
            txt12Percent.Size = new Size(180, 25);
            txt12Percent.ReadOnly = true;

            Label lbl9Percent = new Label();
            lbl9Percent.Text = "استقطاع 9%:";
            lbl9Percent.Location = new Point(600, 150);
            lbl9Percent.AutoSize = true;

            txt9Percent = new TextBox();
            txt9Percent.Location = new Point(400, 150);
            txt9Percent.Size = new Size(180, 25);
            txt9Percent.ReadOnly = true;

            Label lbl3Percent = new Label();
            lbl3Percent.Text = "استقطاع 3%:";
            lbl3Percent.Location = new Point(600, 180);
            lbl3Percent.AutoSize = true;

            txt3Percent = new TextBox();
            txt3Percent.Location = new Point(400, 180);
            txt3Percent.Size = new Size(180, 25);
            txt3Percent.ReadOnly = true;

            // إضافة 4 خانات 1%
            Label lbl1Percent1 = new Label();
            lbl1Percent1.Text = "استقطاع 1% (الأول):";
            lbl1Percent1.Location = new Point(600, 210);
            lbl1Percent1.AutoSize = true;

            txt1Percent1 = new TextBox();
            txt1Percent1.Location = new Point(400, 210);
            txt1Percent1.Size = new Size(180, 25);
            txt1Percent1.ReadOnly = true;

            Label lbl1Percent2 = new Label();
            lbl1Percent2.Text = "استقطاع 1% (الثاني):";
            lbl1Percent2.Location = new Point(600, 240);
            lbl1Percent2.AutoSize = true;

            txt1Percent2 = new TextBox();
            txt1Percent2.Location = new Point(400, 240);
            txt1Percent2.Size = new Size(180, 25);
            txt1Percent2.ReadOnly = true;

            Label lbl1Percent3 = new Label();
            lbl1Percent3.Text = "استقطاع 1% (الثالث):";
            lbl1Percent3.Location = new Point(600, 270);
            lbl1Percent3.AutoSize = true;

            txt1Percent3 = new TextBox();
            txt1Percent3.Location = new Point(400, 270);
            txt1Percent3.Size = new Size(180, 25);
            txt1Percent3.ReadOnly = true;

            Label lbl1Percent4 = new Label();
            lbl1Percent4.Text = "استقطاع 1% (الرابع):";
            lbl1Percent4.Location = new Point(600, 300);
            lbl1Percent4.AutoSize = true;

            txt1Percent4 = new TextBox();
            txt1Percent4.Location = new Point(400, 300);
            txt1Percent4.Size = new Size(180, 25);
            txt1Percent4.ReadOnly = true;

            Label lblTotalDeduction = new Label();
            lblTotalDeduction.Text = "إجمالي الاستقطاعات:";
            lblTotalDeduction.Location = new Point(600, 340);
            lblTotalDeduction.AutoSize = true;
            lblTotalDeduction.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

            txtTotalDeduction = new TextBox();
            txtTotalDeduction.Location = new Point(400, 340);
            txtTotalDeduction.Size = new Size(180, 25);
            txtTotalDeduction.ReadOnly = true;
            txtTotalDeduction.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

            Label lblNetAmount = new Label();
            lblNetAmount.Text = "المبلغ الصافي:";
            lblNetAmount.Location = new Point(600, 370);
            lblNetAmount.AutoSize = true;
            lblNetAmount.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

            txtNetAmount = new TextBox();
            txtNetAmount.Location = new Point(400, 370);
            txtNetAmount.Size = new Size(180, 25);
            txtNetAmount.ReadOnly = true;
            txtNetAmount.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

            tabDeductions.Controls.Add(lblAmount);
            tabDeductions.Controls.Add(txtAmount);
            tabDeductions.Controls.Add(btnCalculate);
            tabDeductions.Controls.Add(lblOriginalAmount);
            tabDeductions.Controls.Add(txtOriginalAmount);
            tabDeductions.Controls.Add(lbl12Percent);
            tabDeductions.Controls.Add(txt12Percent);
            tabDeductions.Controls.Add(lbl9Percent);
            tabDeductions.Controls.Add(txt9Percent);
            tabDeductions.Controls.Add(lbl3Percent);
            tabDeductions.Controls.Add(txt3Percent);
            tabDeductions.Controls.Add(lbl1Percent1);
            tabDeductions.Controls.Add(txt1Percent1);
            tabDeductions.Controls.Add(lbl1Percent2);
            tabDeductions.Controls.Add(txt1Percent2);
            tabDeductions.Controls.Add(lbl1Percent3);
            tabDeductions.Controls.Add(txt1Percent3);
            tabDeductions.Controls.Add(lbl1Percent4);
            tabDeductions.Controls.Add(txt1Percent4);
            tabDeductions.Controls.Add(lblTotalDeduction);
            tabDeductions.Controls.Add(txtTotalDeduction);
            tabDeductions.Controls.Add(lblNetAmount);
            tabDeductions.Controls.Add(txtNetAmount);
        }

        private void CreateHealthInsuranceTab()
        {
            // تبويب حساب التأمين الصحي
            tabHealthInsurance = new TabPage("التأمين الصحي");

            Label lblHealthAmount = new Label();
            lblHealthAmount.Text = "المبلغ:";
            lblHealthAmount.Location = new Point(600, 20);
            lblHealthAmount.AutoSize = true;

            txtHealthAmount = new TextBox();
            txtHealthAmount.Location = new Point(400, 20);
            txtHealthAmount.Size = new Size(180, 25);

            btnCalculateHealth = new Button();
            btnCalculateHealth.Text = "حساب";
            btnCalculateHealth.Location = new Point(400, 50);
            btnCalculateHealth.Size = new Size(100, 30);
            btnCalculateHealth.Click += new EventHandler(BtnCalculateHealth_Click);

            Label lblHealth3Percent = new Label();
            lblHealth3Percent.Text = "3%:";
            lblHealth3Percent.Location = new Point(600, 90);
            lblHealth3Percent.AutoSize = true;

            txtHealth3Percent = new TextBox();
            txtHealth3Percent.Location = new Point(400, 90);
            txtHealth3Percent.Size = new Size(180, 25);
            txtHealth3Percent.ReadOnly = true;

            Label lblHealth1Percent = new Label();
            lblHealth1Percent.Text = "1%:";
            lblHealth1Percent.Location = new Point(600, 120);
            lblHealth1Percent.AutoSize = true;

            txtHealth1Percent = new TextBox();
            txtHealth1Percent.Location = new Point(400, 120);
            txtHealth1Percent.Size = new Size(180, 25);
            txtHealth1Percent.ReadOnly = true;

            Label lblHealthTotal = new Label();
            lblHealthTotal.Text = "4%:";
            lblHealthTotal.Location = new Point(600, 150);
            lblHealthTotal.AutoSize = true;
            lblHealthTotal.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

            txtHealthTotal = new TextBox();
            txtHealthTotal.Location = new Point(400, 150);
            txtHealthTotal.Size = new Size(180, 25);
            txtHealthTotal.ReadOnly = true;
            txtHealthTotal.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

            tabHealthInsurance.Controls.Add(lblHealthAmount);
            tabHealthInsurance.Controls.Add(txtHealthAmount);
            tabHealthInsurance.Controls.Add(btnCalculateHealth);
            tabHealthInsurance.Controls.Add(lblHealth3Percent);
            tabHealthInsurance.Controls.Add(txtHealth3Percent);
            tabHealthInsurance.Controls.Add(lblHealth1Percent);
            tabHealthInsurance.Controls.Add(txtHealth1Percent);
            tabHealthInsurance.Controls.Add(lblHealthTotal);
            tabHealthInsurance.Controls.Add(txtHealthTotal);
        }

        private void CreateReversedContributionsTab()
        {
            // تبويب حساب الاشتراكات المعكوسة
            tabReversedContributions = new TabPage("الاشتراكات المعكوسة");

            Label lblReversedAmount = new Label();
            lblReversedAmount.Text = "المبلغ:";
            lblReversedAmount.Location = new Point(600, 20);
            lblReversedAmount.AutoSize = true;

            txtReversedAmount = new TextBox();
            txtReversedAmount.Location = new Point(400, 20);
            txtReversedAmount.Size = new Size(180, 25);

            btnCalculateReversed = new Button();
            btnCalculateReversed.Text = "حساب";
            btnCalculateReversed.Location = new Point(400, 50);
            btnCalculateReversed.Size = new Size(100, 30);
            btnCalculateReversed.Click += new EventHandler(BtnCalculateReversed_Click);

            // زر التلميحات
            Button btnReversedHelp = new Button();
            btnReversedHelp.Text = "تلميحات";
            btnReversedHelp.Location = new Point(520, 50);
            btnReversedHelp.Size = new Size(80, 30);
            btnReversedHelp.BackColor = Color.LightGreen;
            btnReversedHelp.Click += new EventHandler(BtnReversedHelp_Click);

            Label lblReversedResult = new Label();
            lblReversedResult.Text = "النتيجة:";
            lblReversedResult.Location = new Point(600, 90);
            lblReversedResult.AutoSize = true;

            txtReversedResult = new TextBox();
            txtReversedResult.Location = new Point(400, 90);
            txtReversedResult.Size = new Size(180, 25);
            txtReversedResult.ReadOnly = true;

            tabReversedContributions.Controls.Add(lblReversedAmount);
            tabReversedContributions.Controls.Add(txtReversedAmount);
            tabReversedContributions.Controls.Add(btnCalculateReversed);
            tabReversedContributions.Controls.Add(btnReversedHelp);
            tabReversedContributions.Controls.Add(lblReversedResult);
            tabReversedContributions.Controls.Add(txtReversedResult);
        }

        // حساب الاستقطاعات
        private void BtnCalculate_Click(object sender, EventArgs e)
        {
            try
            {
                decimal amount;
                if (decimal.TryParse(txtAmount.Text, out amount))
                {
                    // حساب الاستقطاعات
                    decimal deduction12 = Math.Round(amount * 0.12m, 2);
                    decimal deduction9 = Math.Round(amount * 0.09m, 2);
                    decimal deduction3 = Math.Round(amount * 0.03m, 2);
                    decimal deduction1_1 = Math.Round(amount * 0.01m, 2);
                    decimal deduction1_2 = Math.Round(amount * 0.01m, 2);
                    decimal deduction1_3 = Math.Round(amount * 0.01m, 2);
                    decimal deduction1_4 = Math.Round(amount * 0.01m, 2);

                    // إجمالي الاستقطاعات
                    decimal totalDeduction = deduction12 + deduction9 + deduction3 + deduction1_1 + deduction1_2 + deduction1_3 + deduction1_4;

                    // المبلغ الصافي
                    decimal netAmount = amount - totalDeduction;

                    // عرض النتائج
                    txtOriginalAmount.Text = amount.ToString("N2");
                    txt12Percent.Text = deduction12.ToString("N2");
                    txt9Percent.Text = deduction9.ToString("N2");
                    txt3Percent.Text = deduction3.ToString("N2");
                    txt1Percent1.Text = deduction1_1.ToString("N2");
                    txt1Percent2.Text = deduction1_2.ToString("N2");
                    txt1Percent3.Text = deduction1_3.ToString("N2");
                    txt1Percent4.Text = deduction1_4.ToString("N2");
                    txtTotalDeduction.Text = totalDeduction.ToString("N2");
                    txtNetAmount.Text = netAmount.ToString("N2");
                }
                else
                {
                    MessageBox.Show("الرجاء إدخال قيمة صحيحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("حدث خطأ: {0}", ex.Message), "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // حساب التأمين الصحي
        private void BtnCalculateHealth_Click(object sender, EventArgs e)
        {
            try
            {
                decimal amount;
                if (decimal.TryParse(txtHealthAmount.Text, out amount))
                {
                    // حساب التأمين الصحي (3% و 1%)
                    decimal healthInsurance3Percent = Math.Round(amount * 0.03m, 2);
                    decimal healthInsurance1Percent = Math.Round(amount * 0.01m, 2);
                    decimal totalHealthInsurance = healthInsurance3Percent + healthInsurance1Percent;

                    // عرض النتائج
                    txtHealth3Percent.Text = healthInsurance3Percent.ToString("N2");
                    txtHealth1Percent.Text = healthInsurance1Percent.ToString("N2");
                    txtHealthTotal.Text = totalHealthInsurance.ToString("N2");
                }
                else
                {
                    MessageBox.Show("الرجاء إدخال قيمة صحيحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("حدث خطأ: {0}", ex.Message), "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // حساب الاشتراكات المعكوسة
        private void BtnCalculateReversed_Click(object sender, EventArgs e)
        {
            try
            {
                decimal amount;
                if (decimal.TryParse(txtReversedAmount.Text, out amount))
                {
                    // حساب إجمالي الاشتراكات (قسمة المبلغ على 28.25%)
                    decimal totalContributions = Math.Round(amount / 0.2825m, 2);

                    // عرض النتائج
                    txtReversedResult.Text = totalContributions.ToString("N2");
                }
                else
                {
                    MessageBox.Show("الرجاء إدخال قيمة صحيحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("حدث خطأ: {0}", ex.Message), "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // دالة التلميحات
        private void BtnHelp_Click(object sender, EventArgs e)
        {
            string helpMessage = "المعادلة المستخدمة:\n" +
                               "قيمة القسط × عدد الشهور × نسبة الفائدة ÷ 12 × عدد الشهور ÷ 2\n\n" +
                               "مثال توضيحي:\n" +
                               "29 × 10 شهور × 15.50% ÷ 12 × 10 شهور ÷ 2 = 18.72 جنيه\n\n" +
                               "ملاحظة قانونية:\n" +
                               "طبقاً للمادة 121 من قانون التأمينات، لا يجوز أن تتجاوز إجمالي الغرامة أصل المبلغ المستحق.";

            MessageBox.Show(helpMessage, "تلميحات وشرح المعادلة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        // دالة التلميحات للاشتراكات المعكوسة
        private void BtnReversedHelp_Click(object sender, EventArgs e)
        {
            string helpMessage = "طريقة الحساب:\n" +
                               "المبلغ المدخل يقسم على 28.25%\n\n" +
                               "مثال:\n" +
                               "إذا كان المبلغ 1000 جنيه\n" +
                               "النتيجة = 1000 ÷ 0.2825 = 3539.82 جنيه";

            MessageBox.Show(helpMessage, "تلميحات الاشتراكات المعكوسة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
