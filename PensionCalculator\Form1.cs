using System;
using System.Windows.Forms;
using System.Drawing;

namespace PensionCalculator
{
    public partial class Form1 : Form
    {
        private TabControl tabControl;
        private TabPage tabDeductions;
        private TabPage tabHealthInsurance;
        private TabPage tabReversedContributions;
        private TabPage tabDelayInterest;

        // تبويب حساب الاستقطاعات
        private TextBox txtAmount;
        private Button btnCalculate;
        private TextBox txtOriginalAmount;
        private TextBox txt12Percent;
        private TextBox txt9Percent;
        private TextBox txt3Percent;
        private TextBox txt1Percent1;
        private TextBox txt1Percent2;
        private TextBox txt1Percent3;
        private TextBox txt1Percent4;
        private TextBox txt025Percent;
        private TextBox txtTotalDeduction;
        private TextBox txtNetAmount;

        // تبويب حساب التأمين الصحي
        private TextBox txtHealthAmount;
        private Button btnCalculateHealth;
        private TextBox txtHealth3Percent;
        private TextBox txtHealth1Percent;
        private TextBox txtHealthTotal;

        // تبويب حساب الاشتراكات المعكوسة
        private TextBox txtReversedAmount;
        private Button btnCalculateReversed;
        private TextBox txtReversedResult;

        // تبويب حساب فوائد التأخير
        private TextBox txtInstallmentValue;
        private TextBox txtDelayMonths;
        private TextBox txtInterestRate;
        private Button btnCalculateDelayInterest;
        private TextBox txtDelayInterestResult;

        public Form1()
        {
            InitializeComponent();
            InitializeCustomComponents();
        }

        private void InitializeCustomComponents()
        {
            this.Text = "نظام المحاسبة المالية";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 10F);

            // العنوان
            Label lblTitle = new Label();
            lblTitle.Text = "نظام المحاسبة المالية";
            lblTitle.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            lblTitle.AutoSize = true;
            lblTitle.Location = new Point(300, 20);
            this.Controls.Add(lblTitle);

            // إنشاء التبويبات
            tabControl = new TabControl();
            tabControl.Location = new Point(20, 60);
            tabControl.Size = new Size(750, 480);

            CreateDeductionsTab();
            CreateHealthInsuranceTab();
            CreateReversedContributionsTab();
            CreateDelayInterestTab();

            // إضافة التبويبات إلى التحكم
            tabControl.Controls.Add(tabDeductions);
            tabControl.Controls.Add(tabHealthInsurance);
            tabControl.Controls.Add(tabReversedContributions);
            tabControl.Controls.Add(tabDelayInterest);

            this.Controls.Add(tabControl);
        }

        private void CreateDelayInterestTab()
        {
            // تبويب حساب فوائد التأخير
            tabDelayInterest = new TabPage("فوائد التأخير");

            Label lblInstallmentValue = new Label();
            lblInstallmentValue.Text = "قيمة القسط:";
            lblInstallmentValue.Location = new Point(600, 20);
            lblInstallmentValue.AutoSize = true;

            txtInstallmentValue = new TextBox();
            txtInstallmentValue.Location = new Point(400, 20);
            txtInstallmentValue.Size = new Size(180, 25);

            Label lblDelayMonths = new Label();
            lblDelayMonths.Text = "عدد شهور التأخير:";
            lblDelayMonths.Location = new Point(600, 60);
            lblDelayMonths.AutoSize = true;

            txtDelayMonths = new TextBox();
            txtDelayMonths.Location = new Point(400, 60);
            txtDelayMonths.Size = new Size(180, 25);

            Label lblInterestRate = new Label();
            lblInterestRate.Text = "نسبة الفائدة (%):";
            lblInterestRate.Location = new Point(600, 100);
            lblInterestRate.AutoSize = true;

            txtInterestRate = new TextBox();
            txtInterestRate.Location = new Point(400, 100);
            txtInterestRate.Size = new Size(180, 25);
            txtInterestRate.Text = "15.50"; // القيمة الافتراضية

            btnCalculateDelayInterest = new Button();
            btnCalculateDelayInterest.Text = "حساب فوائد التأخير";
            btnCalculateDelayInterest.Location = new Point(400, 140);
            btnCalculateDelayInterest.Size = new Size(150, 30);
            btnCalculateDelayInterest.Click += new EventHandler(BtnCalculateDelayInterest_Click);

            Label lblDelayInterestResult = new Label();
            lblDelayInterestResult.Text = "إجمالي فوائد التأخير:";
            lblDelayInterestResult.Location = new Point(600, 190);
            lblDelayInterestResult.AutoSize = true;
            lblDelayInterestResult.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

            txtDelayInterestResult = new TextBox();
            txtDelayInterestResult.Location = new Point(400, 190);
            txtDelayInterestResult.Size = new Size(180, 25);
            txtDelayInterestResult.ReadOnly = true;
            txtDelayInterestResult.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            txtDelayInterestResult.BackColor = Color.LightYellow;

            // إضافة شرح للمعادلة
            Label lblFormula = new Label();
            lblFormula.Text = "المعادلة: قيمة القسط × عدد الشهور × نسبة الفائدة ÷ 12 × عدد الشهور ÷ 2";
            lblFormula.Location = new Point(50, 240);
            lblFormula.Size = new Size(650, 20);
            lblFormula.Font = new Font("Segoe UI", 9F, FontStyle.Italic);
            lblFormula.ForeColor = Color.DarkBlue;

            Label lblExample = new Label();
            lblExample.Text = "مثال: 29 × 10 شهور × 15.50% ÷ 12 × 10 شهور ÷ 2 = 18.72 جنيه";
            lblExample.Location = new Point(50, 270);
            lblExample.Size = new Size(650, 20);
            lblExample.Font = new Font("Segoe UI", 9F, FontStyle.Italic);
            lblExample.ForeColor = Color.DarkGreen;

            tabDelayInterest.Controls.Add(lblInstallmentValue);
            tabDelayInterest.Controls.Add(txtInstallmentValue);
            tabDelayInterest.Controls.Add(lblDelayMonths);
            tabDelayInterest.Controls.Add(txtDelayMonths);
            tabDelayInterest.Controls.Add(lblInterestRate);
            tabDelayInterest.Controls.Add(txtInterestRate);
            tabDelayInterest.Controls.Add(btnCalculateDelayInterest);
            tabDelayInterest.Controls.Add(lblDelayInterestResult);
            tabDelayInterest.Controls.Add(txtDelayInterestResult);
            tabDelayInterest.Controls.Add(lblFormula);
            tabDelayInterest.Controls.Add(lblExample);
        }

        // حساب فوائد التأخير
        private void BtnCalculateDelayInterest_Click(object sender, EventArgs e)
        {
            try
            {
                decimal installmentValue, delayMonths, interestRate;

                if (decimal.TryParse(txtInstallmentValue.Text, out installmentValue) &&
                    decimal.TryParse(txtDelayMonths.Text, out delayMonths) &&
                    decimal.TryParse(txtInterestRate.Text, out interestRate))
                {
                    // تطبيق المعادلة: قيمة القسط × عدد الشهور × نسبة الفائدة ÷ 12 × عدد الشهور ÷ 2
                    // المعادلة المبسطة: قيمة القسط × (عدد الشهور)² × نسبة الفائدة ÷ 12 ÷ 2
                    decimal delayInterest = Math.Round(
                        installmentValue * delayMonths * (interestRate / 100m) / 12m * delayMonths / 2m,
                        2
                    );

                    // عرض النتائج
                    txtDelayInterestResult.Text = delayInterest.ToString("N2") + " جنيه";
                }
                else
                {
                    MessageBox.Show("الرجاء إدخال قيم صحيحة في جميع الحقول", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("حدث خطأ: {0}", ex.Message), "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // باقي الدوال - سأضيفها في الجزء التالي
        private void CreateDeductionsTab() { /* سيتم إضافتها */ }
        private void CreateHealthInsuranceTab() { /* سيتم إضافتها */ }
        private void CreateReversedContributionsTab() { /* سيتم إضافتها */ }
    }
}
