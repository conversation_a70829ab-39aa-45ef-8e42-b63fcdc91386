using System;
using System.Windows.Forms;
using System.Drawing;

public class FullCalculator : Form
{
    private TabControl tabControl;
    private TabPage tabDeductions;
    private TabPage tabHealthInsurance;
    private TabPage tabReversedContributions;
    private TabPage tabDelayInterest;

    // تبويب حساب الاستقطاعات
    private TextBox txtAmount;
    private Button btnCalculate;
    private TextBox txtOriginalAmount;
    private TextBox txt12Percent;
    private TextBox txt9Percent;
    private TextBox txt3Percent;
    private TextBox txt1Percent1;
    private TextBox txt1Percent2;
    private TextBox txt1Percent3;
    private TextBox txt1Percent4;
    private TextBox txt025Percent;
    private TextBox txtTotalDeduction;
    private TextBox txtNetAmount;

    // تبويب حساب التأمين الصحي
    private TextBox txtHealthAmount;
    private Button btnCalculateHealth;
    private TextBox txtHealth3Percent;
    private TextBox txtHealth1Percent;
    private TextBox txtHealthTotal;

    // تبويب حساب الاشتراكات المعكوسة
    private TextBox txtReversedAmount;
    private Button btnCalculateReversed;
    private TextBox txtReversedResult;

    // تبويب حساب فوائد التأخير
    private TextBox txtInstallmentValue;
    private TextBox txtDelayMonths;
    private TextBox txtInterestRate;
    private Button btnCalculateDelayInterest;
    private TextBox txtDelayInterestResult;

    public FullCalculator()
    {
        this.Text = "نظام المحاسبة المالية";
        this.Size = new Size(800, 600);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.FormBorderStyle = FormBorderStyle.FixedDialog;
        this.MaximizeBox = false;
        this.RightToLeft = RightToLeft.Yes;
        this.RightToLeftLayout = true;
        this.Font = new Font("Segoe UI", 10F);

        // العنوان
        Label lblTitle = new Label();
        lblTitle.Text = "نظام المحاسبة المالية";
        lblTitle.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
        lblTitle.AutoSize = true;
        lblTitle.Location = new Point(300, 20);
        this.Controls.Add(lblTitle);

        // إنشاء التبويبات
        tabControl = new TabControl();
        tabControl.Location = new Point(20, 60);
        tabControl.Size = new Size(750, 480);

        // تبويب حساب الاستقطاعات
        tabDeductions = new TabPage("حساب الاستقطاعات");

        Label lblAmount = new Label();
        lblAmount.Text = "المبلغ:";
        lblAmount.Location = new Point(600, 20);
        lblAmount.AutoSize = true;

        txtAmount = new TextBox();
        txtAmount.Location = new Point(400, 20);
        txtAmount.Size = new Size(180, 25);

        btnCalculate = new Button();
        btnCalculate.Text = "حساب";
        btnCalculate.Location = new Point(400, 50);
        btnCalculate.Size = new Size(100, 30);
        btnCalculate.Click += new EventHandler(BtnCalculate_Click);

        // النتائج
        Label lblOriginalAmount = new Label();
        lblOriginalAmount.Text = "المبلغ الأساسي:";
        lblOriginalAmount.Location = new Point(600, 90);
        lblOriginalAmount.AutoSize = true;

        txtOriginalAmount = new TextBox();
        txtOriginalAmount.Location = new Point(400, 90);
        txtOriginalAmount.Size = new Size(180, 25);
        txtOriginalAmount.ReadOnly = true;

        Label lbl12Percent = new Label();
        lbl12Percent.Text = "استقطاع 12%:";
        lbl12Percent.Location = new Point(600, 120);
        lbl12Percent.AutoSize = true;

        txt12Percent = new TextBox();
        txt12Percent.Location = new Point(400, 120);
        txt12Percent.Size = new Size(180, 25);
        txt12Percent.ReadOnly = true;

        Label lbl9Percent = new Label();
        lbl9Percent.Text = "استقطاع 9%:";
        lbl9Percent.Location = new Point(600, 150);
        lbl9Percent.AutoSize = true;

        txt9Percent = new TextBox();
        txt9Percent.Location = new Point(400, 150);
        txt9Percent.Size = new Size(180, 25);
        txt9Percent.ReadOnly = true;

        Label lbl3Percent = new Label();
        lbl3Percent.Text = "استقطاع 3%:";
        lbl3Percent.Location = new Point(600, 180);
        lbl3Percent.AutoSize = true;

        txt3Percent = new TextBox();
        txt3Percent.Location = new Point(400, 180);
        txt3Percent.Size = new Size(180, 25);
        txt3Percent.ReadOnly = true;

        Label lbl1Percent1 = new Label();
        lbl1Percent1.Text = "استقطاع 1% (1):";
        lbl1Percent1.Location = new Point(600, 210);
        lbl1Percent1.AutoSize = true;

        txt1Percent1 = new TextBox();
        txt1Percent1.Location = new Point(400, 210);
        txt1Percent1.Size = new Size(180, 25);
        txt1Percent1.ReadOnly = true;

        Label lbl1Percent2 = new Label();
        lbl1Percent2.Text = "استقطاع 1% (2):";
        lbl1Percent2.Location = new Point(600, 240);
        lbl1Percent2.AutoSize = true;

        txt1Percent2 = new TextBox();
        txt1Percent2.Location = new Point(400, 240);
        txt1Percent2.Size = new Size(180, 25);
        txt1Percent2.ReadOnly = true;

        Label lbl1Percent3 = new Label();
        lbl1Percent3.Text = "استقطاع 1% (3):";
        lbl1Percent3.Location = new Point(600, 270);
        lbl1Percent3.AutoSize = true;

        txt1Percent3 = new TextBox();
        txt1Percent3.Location = new Point(400, 270);
        txt1Percent3.Size = new Size(180, 25);
        txt1Percent3.ReadOnly = true;

        Label lbl1Percent4 = new Label();
        lbl1Percent4.Text = "استقطاع 1% (4):";
        lbl1Percent4.Location = new Point(600, 300);
        lbl1Percent4.AutoSize = true;

        txt1Percent4 = new TextBox();
        txt1Percent4.Location = new Point(400, 300);
        txt1Percent4.Size = new Size(180, 25);
        txt1Percent4.ReadOnly = true;

        Label lbl025Percent = new Label();
        lbl025Percent.Text = "استقطاع 0.25%:";
        lbl025Percent.Location = new Point(600, 330);
        lbl025Percent.AutoSize = true;

        txt025Percent = new TextBox();
        txt025Percent.Location = new Point(400, 330);
        txt025Percent.Size = new Size(180, 25);
        txt025Percent.ReadOnly = true;

        Label lblTotalDeduction = new Label();
        lblTotalDeduction.Text = "إجمالي الاستقطاعات:";
        lblTotalDeduction.Location = new Point(600, 360);
        lblTotalDeduction.AutoSize = true;
        lblTotalDeduction.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

        txtTotalDeduction = new TextBox();
        txtTotalDeduction.Location = new Point(400, 360);
        txtTotalDeduction.Size = new Size(180, 25);
        txtTotalDeduction.ReadOnly = true;
        txtTotalDeduction.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

        Label lblNetAmount = new Label();
        lblNetAmount.Text = "المبلغ الصافي:";
        lblNetAmount.Location = new Point(600, 390);
        lblNetAmount.AutoSize = true;
        lblNetAmount.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

        txtNetAmount = new TextBox();
        txtNetAmount.Location = new Point(400, 390);
        txtNetAmount.Size = new Size(180, 25);
        txtNetAmount.ReadOnly = true;
        txtNetAmount.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

        tabDeductions.Controls.Add(lblAmount);
        tabDeductions.Controls.Add(txtAmount);
        tabDeductions.Controls.Add(btnCalculate);
        tabDeductions.Controls.Add(lblOriginalAmount);
        tabDeductions.Controls.Add(txtOriginalAmount);
        tabDeductions.Controls.Add(lbl12Percent);
        tabDeductions.Controls.Add(txt12Percent);
        tabDeductions.Controls.Add(lbl9Percent);
        tabDeductions.Controls.Add(txt9Percent);
        tabDeductions.Controls.Add(lbl3Percent);
        tabDeductions.Controls.Add(txt3Percent);
        tabDeductions.Controls.Add(lbl1Percent1);
        tabDeductions.Controls.Add(txt1Percent1);
        tabDeductions.Controls.Add(lbl1Percent2);
        tabDeductions.Controls.Add(txt1Percent2);
        tabDeductions.Controls.Add(lbl1Percent3);
        tabDeductions.Controls.Add(txt1Percent3);
        tabDeductions.Controls.Add(lbl1Percent4);
        tabDeductions.Controls.Add(txt1Percent4);
        tabDeductions.Controls.Add(lbl025Percent);
        tabDeductions.Controls.Add(txt025Percent);
        tabDeductions.Controls.Add(lblTotalDeduction);
        tabDeductions.Controls.Add(txtTotalDeduction);
        tabDeductions.Controls.Add(lblNetAmount);
        tabDeductions.Controls.Add(txtNetAmount);

        // تبويب حساب التأمين الصحي
        tabHealthInsurance = new TabPage("التأمين الصحي");

        Label lblHealthAmount = new Label();
        lblHealthAmount.Text = "المبلغ:";
        lblHealthAmount.Location = new Point(600, 20);
        lblHealthAmount.AutoSize = true;

        txtHealthAmount = new TextBox();
        txtHealthAmount.Location = new Point(400, 20);
        txtHealthAmount.Size = new Size(180, 25);

        btnCalculateHealth = new Button();
        btnCalculateHealth.Text = "حساب";
        btnCalculateHealth.Location = new Point(400, 50);
        btnCalculateHealth.Size = new Size(100, 30);
        btnCalculateHealth.Click += new EventHandler(BtnCalculateHealth_Click);

        Label lblHealth3Percent = new Label();
        lblHealth3Percent.Text = "قيمة التأمين الصحي (3%):";
        lblHealth3Percent.Location = new Point(600, 90);
        lblHealth3Percent.AutoSize = true;

        txtHealth3Percent = new TextBox();
        txtHealth3Percent.Location = new Point(400, 90);
        txtHealth3Percent.Size = new Size(180, 25);
        txtHealth3Percent.ReadOnly = true;

        Label lblHealth1Percent = new Label();
        lblHealth1Percent.Text = "قيمة التأمين الصحي (1%):";
        lblHealth1Percent.Location = new Point(600, 120);
        lblHealth1Percent.AutoSize = true;

        txtHealth1Percent = new TextBox();
        txtHealth1Percent.Location = new Point(400, 120);
        txtHealth1Percent.Size = new Size(180, 25);
        txtHealth1Percent.ReadOnly = true;

        Label lblHealthTotal = new Label();
        lblHealthTotal.Text = "إجمالي التأمين الصحي (4%):";
        lblHealthTotal.Location = new Point(600, 150);
        lblHealthTotal.AutoSize = true;
        lblHealthTotal.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

        txtHealthTotal = new TextBox();
        txtHealthTotal.Location = new Point(400, 150);
        txtHealthTotal.Size = new Size(180, 25);
        txtHealthTotal.ReadOnly = true;
        txtHealthTotal.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

        tabHealthInsurance.Controls.Add(lblHealthAmount);
        tabHealthInsurance.Controls.Add(txtHealthAmount);
        tabHealthInsurance.Controls.Add(btnCalculateHealth);
        tabHealthInsurance.Controls.Add(lblHealth3Percent);
        tabHealthInsurance.Controls.Add(txtHealth3Percent);
        tabHealthInsurance.Controls.Add(lblHealth1Percent);
        tabHealthInsurance.Controls.Add(txtHealth1Percent);
        tabHealthInsurance.Controls.Add(lblHealthTotal);
        tabHealthInsurance.Controls.Add(txtHealthTotal);

        // تبويب حساب الاشتراكات المعكوسة
        tabReversedContributions = new TabPage("الاشتراكات المعكوسة");

        Label lblReversedAmount = new Label();
        lblReversedAmount.Text = "المبلغ:";
        lblReversedAmount.Location = new Point(600, 20);
        lblReversedAmount.AutoSize = true;

        txtReversedAmount = new TextBox();
        txtReversedAmount.Location = new Point(400, 20);
        txtReversedAmount.Size = new Size(180, 25);

        btnCalculateReversed = new Button();
        btnCalculateReversed.Text = "حساب";
        btnCalculateReversed.Location = new Point(400, 50);
        btnCalculateReversed.Size = new Size(100, 30);
        btnCalculateReversed.Click += new EventHandler(BtnCalculateReversed_Click);

        Label lblReversedResult = new Label();
        lblReversedResult.Text = "المبلغ (قسمة على 28.25%):";
        lblReversedResult.Location = new Point(600, 90);
        lblReversedResult.AutoSize = true;

        txtReversedResult = new TextBox();
        txtReversedResult.Location = new Point(400, 90);
        txtReversedResult.Size = new Size(180, 25);
        txtReversedResult.ReadOnly = true;

        tabReversedContributions.Controls.Add(lblReversedAmount);
        tabReversedContributions.Controls.Add(txtReversedAmount);
        tabReversedContributions.Controls.Add(btnCalculateReversed);
        tabReversedContributions.Controls.Add(lblReversedResult);
        tabReversedContributions.Controls.Add(txtReversedResult);

        // تبويب حساب فوائد التأخير
        tabDelayInterest = new TabPage("فوائد التأخير");

        Label lblInstallmentValue = new Label();
        lblInstallmentValue.Text = "قيمة القسط:";
        lblInstallmentValue.Location = new Point(600, 20);
        lblInstallmentValue.AutoSize = true;

        txtInstallmentValue = new TextBox();
        txtInstallmentValue.Location = new Point(400, 20);
        txtInstallmentValue.Size = new Size(180, 25);

        Label lblDelayMonths = new Label();
        lblDelayMonths.Text = "عدد شهور التأخير:";
        lblDelayMonths.Location = new Point(600, 60);
        lblDelayMonths.AutoSize = true;

        txtDelayMonths = new TextBox();
        txtDelayMonths.Location = new Point(400, 60);
        txtDelayMonths.Size = new Size(180, 25);

        Label lblInterestRate = new Label();
        lblInterestRate.Text = "نسبة الفائدة (%):";
        lblInterestRate.Location = new Point(600, 100);
        lblInterestRate.AutoSize = true;

        txtInterestRate = new TextBox();
        txtInterestRate.Location = new Point(400, 100);
        txtInterestRate.Size = new Size(180, 25);
        txtInterestRate.Text = "15.50"; // القيمة الافتراضية

        btnCalculateDelayInterest = new Button();
        btnCalculateDelayInterest.Text = "حساب فوائد التأخير";
        btnCalculateDelayInterest.Location = new Point(400, 140);
        btnCalculateDelayInterest.Size = new Size(150, 30);
        btnCalculateDelayInterest.Click += new EventHandler(BtnCalculateDelayInterest_Click);

        Label lblDelayInterestResult = new Label();
        lblDelayInterestResult.Text = "إجمالي فوائد التأخير:";
        lblDelayInterestResult.Location = new Point(600, 190);
        lblDelayInterestResult.AutoSize = true;
        lblDelayInterestResult.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

        txtDelayInterestResult = new TextBox();
        txtDelayInterestResult.Location = new Point(400, 190);
        txtDelayInterestResult.Size = new Size(180, 25);
        txtDelayInterestResult.ReadOnly = true;
        txtDelayInterestResult.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
        txtDelayInterestResult.BackColor = Color.LightYellow;

        // إضافة شرح للمعادلة
        Label lblFormula = new Label();
        lblFormula.Text = "المعادلة: قيمة القسط × عدد الشهور × نسبة الفائدة ÷ 12 × عدد الشهور ÷ 2";
        lblFormula.Location = new Point(50, 240);
        lblFormula.Size = new Size(650, 20);
        lblFormula.Font = new Font("Segoe UI", 9F, FontStyle.Italic);
        lblFormula.ForeColor = Color.DarkBlue;

        Label lblExample = new Label();
        lblExample.Text = "مثال: 29 × 10 شهور × 15.50% ÷ 12 × 10 شهور ÷ 2 = 18.72 جنيه";
        lblExample.Location = new Point(50, 270);
        lblExample.Size = new Size(650, 20);
        lblExample.Font = new Font("Segoe UI", 9F, FontStyle.Italic);
        lblExample.ForeColor = Color.DarkGreen;

        tabDelayInterest.Controls.Add(lblInstallmentValue);
        tabDelayInterest.Controls.Add(txtInstallmentValue);
        tabDelayInterest.Controls.Add(lblDelayMonths);
        tabDelayInterest.Controls.Add(txtDelayMonths);
        tabDelayInterest.Controls.Add(lblInterestRate);
        tabDelayInterest.Controls.Add(txtInterestRate);
        tabDelayInterest.Controls.Add(btnCalculateDelayInterest);
        tabDelayInterest.Controls.Add(lblDelayInterestResult);
        tabDelayInterest.Controls.Add(txtDelayInterestResult);
        tabDelayInterest.Controls.Add(lblFormula);
        tabDelayInterest.Controls.Add(lblExample);

        // إضافة التبويبات إلى التحكم
        tabControl.Controls.Add(tabDeductions);
        tabControl.Controls.Add(tabHealthInsurance);
        tabControl.Controls.Add(tabReversedContributions);
        tabControl.Controls.Add(tabDelayInterest);

        this.Controls.Add(tabControl);
    }

    // حساب الاستقطاعات
    private void BtnCalculate_Click(object sender, EventArgs e)
    {
        try
        {
            decimal amount;
            if (decimal.TryParse(txtAmount.Text, out amount))
            {
                // حساب الاستقطاعات
                decimal deduction12 = Math.Round(amount * 0.12m, 2);
                decimal deduction9 = Math.Round(amount * 0.09m, 2);
                decimal deduction3 = Math.Round(amount * 0.03m, 2);
                decimal deduction1_1 = Math.Round(amount * 0.01m, 2);
                decimal deduction1_2 = Math.Round(amount * 0.01m, 2);
                decimal deduction1_3 = Math.Round(amount * 0.01m, 2);
                decimal deduction1_4 = Math.Round(amount * 0.01m, 2);
                decimal deduction025 = Math.Round(amount * 0.0025m, 2);

                // إجمالي الاستقطاعات
                decimal totalDeduction = deduction12 + deduction9 + deduction3 +
                                        deduction1_1 + deduction1_2 + deduction1_3 +
                                        deduction1_4 + deduction025;

                // المبلغ الصافي
                decimal netAmount = amount - totalDeduction;

                // عرض النتائج
                txtOriginalAmount.Text = amount.ToString("N2");
                txt12Percent.Text = deduction12.ToString("N2");
                txt9Percent.Text = deduction9.ToString("N2");
                txt3Percent.Text = deduction3.ToString("N2");
                txt1Percent1.Text = deduction1_1.ToString("N2");
                txt1Percent2.Text = deduction1_2.ToString("N2");
                txt1Percent3.Text = deduction1_3.ToString("N2");
                txt1Percent4.Text = deduction1_4.ToString("N2");
                txt025Percent.Text = deduction025.ToString("N2");
                txtTotalDeduction.Text = totalDeduction.ToString("N2");
                txtNetAmount.Text = netAmount.ToString("N2");
            }
            else
            {
                MessageBox.Show("الرجاء إدخال قيمة صحيحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show(string.Format("حدث خطأ: {0}", ex.Message), "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    // حساب التأمين الصحي
    private void BtnCalculateHealth_Click(object sender, EventArgs e)
    {
        try
        {
            decimal amount;
            if (decimal.TryParse(txtHealthAmount.Text, out amount))
            {
                // حساب التأمين الصحي (3% و 1%)
                decimal healthInsurance3Percent = Math.Round(amount * 0.03m, 2);
                decimal healthInsurance1Percent = Math.Round(amount * 0.01m, 2);
                decimal totalHealthInsurance = healthInsurance3Percent + healthInsurance1Percent;

                // عرض النتائج
                txtHealth3Percent.Text = healthInsurance3Percent.ToString("N2");
                txtHealth1Percent.Text = healthInsurance1Percent.ToString("N2");
                txtHealthTotal.Text = totalHealthInsurance.ToString("N2");
            }
            else
            {
                MessageBox.Show("الرجاء إدخال قيمة صحيحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show(string.Format("حدث خطأ: {0}", ex.Message), "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    // حساب الاشتراكات المعكوسة
    private void BtnCalculateReversed_Click(object sender, EventArgs e)
    {
        try
        {
            decimal amount;
            if (decimal.TryParse(txtReversedAmount.Text, out amount))
            {
                // حساب إجمالي الاشتراكات (قسمة المبلغ على 28.25%)
                decimal totalContributions = Math.Round(amount / 0.2825m, 2);

                // عرض النتائج
                txtReversedResult.Text = totalContributions.ToString("N2");
            }
            else
            {
                MessageBox.Show("الرجاء إدخال قيمة صحيحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show(string.Format("حدث خطأ: {0}", ex.Message), "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    // حساب فوائد التأخير
    private void BtnCalculateDelayInterest_Click(object sender, EventArgs e)
    {
        try
        {
            decimal installmentValue, delayMonths, interestRate;

            if (decimal.TryParse(txtInstallmentValue.Text, out installmentValue) &&
                decimal.TryParse(txtDelayMonths.Text, out delayMonths) &&
                decimal.TryParse(txtInterestRate.Text, out interestRate))
            {
                // تطبيق المعادلة: قيمة القسط × عدد الشهور × نسبة الفائدة ÷ 12 × عدد الشهور ÷ 2
                // المعادلة المبسطة: قيمة القسط × (عدد الشهور)² × نسبة الفائدة ÷ 12 ÷ 2
                decimal delayInterest = Math.Round(
                    installmentValue * delayMonths * (interestRate / 100m) / 12m * delayMonths / 2m,
                    2
                );

                // عرض النتائج
                txtDelayInterestResult.Text = delayInterest.ToString("N2") + " جنيه";
            }
            else
            {
                MessageBox.Show("الرجاء إدخال قيم صحيحة في جميع الحقول", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show(string.Format("حدث خطأ: {0}", ex.Message), "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    [STAThread]
    public static void Main()
    {
        Application.EnableVisualStyles();
        Application.SetCompatibleTextRenderingDefault(false);
        Application.Run(new FullCalculator());
    }
}
