@echo off
chcp 65001 >nul
title إنشاء حزمة نظام حسابات المعاشات المحمولة

echo ===============================================
echo      إنشاء حزمة نظام حسابات المعاشات
echo      إعداد: أ/ أحمد ابراهيم
echo ===============================================
echo.

REM إنشاء مجلد الحزمة المحمولة
set PACKAGE_DIR=نظام_حسابات_المعاشات_محمول
if exist "%PACKAGE_DIR%" (
    echo 🗑️ حذف المجلد القديم...
    rmdir /s /q "%PACKAGE_DIR%"
)

echo 📁 إنشاء مجلد جديد...
mkdir "%PACKAGE_DIR%"

REM نسخ الملف التنفيذي
echo 📋 نسخ الملف التنفيذي...
if exist "PensionCalculator\bin\Release\net6.0-windows\PensionCalculator.exe" (
    copy "PensionCalculator\bin\Release\net6.0-windows\PensionCalculator.exe" "%PACKAGE_DIR%\"
    echo ✅ تم نسخ الملف التنفيذي
) else (
    echo ❌ خطأ: لم يتم العثور على الملف التنفيذي
    echo يرجى بناء المشروع أولاً
    pause
    exit /b 1
)

REM نسخ ملفات التوثيق
echo 📋 نسخ ملفات التوثيق...
copy "README_نظام_حسابات_المعاشات.txt" "%PACKAGE_DIR%\"
copy "دليل_التثبيت_والنقل.txt" "%PACKAGE_DIR%\"
copy "تشغيل_نظام_المعاشات.bat" "%PACKAGE_DIR%\"

REM إنشاء ملف معلومات الإصدار
echo 📋 إنشاء ملف معلومات الإصدار...
(
echo نظام حسابات المعاشات - النسخة المحمولة
echo ===============================================
echo الإصدار: 1.0
echo التاريخ: %date%
echo الوقت: %time%
echo المطور: أ/ أحمد ابراهيم
echo ===============================================
echo.
echo محتويات الحزمة:
echo • PensionCalculator.exe - الملف التنفيذي الرئيسي
echo • تشغيل_نظام_المعاشات.bat - ملف التشغيل السريع
echo • README_نظام_حسابات_المعاشات.txt - دليل الاستخدام
echo • دليل_التثبيت_والنقل.txt - دليل النقل والتثبيت
echo • معلومات_الإصدار.txt - هذا الملف
echo.
echo للتشغيل: انقر نقراً مزدوجاً على PensionCalculator.exe
echo أو استخدم ملف تشغيل_نظام_المعاشات.bat
) > "%PACKAGE_DIR%\معلومات_الإصدار.txt"

echo.
echo ✅ تم إنشاء الحزمة المحمولة بنجاح!
echo.
echo 📁 مكان الحزمة: %PACKAGE_DIR%
echo.
echo 📋 محتويات الحزمة:
dir /b "%PACKAGE_DIR%"
echo.
echo 🚀 يمكنك الآن نسخ مجلد "%PACKAGE_DIR%" إلى أي جهاز آخر
echo.
echo هل تريد فتح مجلد الحزمة؟ (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    explorer "%PACKAGE_DIR%"
)

echo.
echo ✅ تم الانتهاء بنجاح!
pause
