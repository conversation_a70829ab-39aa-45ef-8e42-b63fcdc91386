using System;
using System.Windows.Forms;
using System.Drawing;

namespace DelayInterestApp
{
    public partial class Form1 : Form
    {
        private TextBox txtInstallmentValue;
        private TextBox txtDelayMonths;
        private TextBox txtInterestRate;
        private Button btnCalculate;
        private TextBox txtResult;

        public Form1()
        {
            InitializeComponent();
            InitializeCustomComponents();
        }

        private void InitializeCustomComponents()
        {
            this.Text = "حساب غرامة الأقساط";
            this.Size = new Size(600, 350);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 10F);

            // العنوان
            Label lblTitle = new Label();
            lblTitle.Text = "حساب غرامة الأقساط";
            lblTitle.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            lblTitle.AutoSize = true;
            lblTitle.Location = new Point(200, 20);
            this.Controls.Add(lblTitle);

            // قيمة القسط
            Label lblInstallmentValue = new Label();
            lblInstallmentValue.Text = "قيمة القسط:";
            lblInstallmentValue.Location = new Point(450, 80);
            lblInstallmentValue.AutoSize = true;
            this.Controls.Add(lblInstallmentValue);

            txtInstallmentValue = new TextBox();
            txtInstallmentValue.Location = new Point(250, 80);
            txtInstallmentValue.Size = new Size(180, 25);
            this.Controls.Add(txtInstallmentValue);

            // عدد شهور التأخير
            Label lblDelayMonths = new Label();
            lblDelayMonths.Text = "عدد شهور التأخير:";
            lblDelayMonths.Location = new Point(450, 120);
            lblDelayMonths.AutoSize = true;
            this.Controls.Add(lblDelayMonths);

            txtDelayMonths = new TextBox();
            txtDelayMonths.Location = new Point(250, 120);
            txtDelayMonths.Size = new Size(180, 25);
            this.Controls.Add(txtDelayMonths);

            // نسبة الفائدة
            Label lblInterestRate = new Label();
            lblInterestRate.Text = "نسبة الفائدة (%):";
            lblInterestRate.Location = new Point(450, 160);
            lblInterestRate.AutoSize = true;
            this.Controls.Add(lblInterestRate);

            txtInterestRate = new TextBox();
            txtInterestRate.Location = new Point(250, 160);
            txtInterestRate.Size = new Size(180, 25);
            txtInterestRate.Text = "15.50"; // القيمة الافتراضية
            this.Controls.Add(txtInterestRate);

            // زر الحساب
            btnCalculate = new Button();
            btnCalculate.Text = "حساب غرامة تأخير الأقساط";
            btnCalculate.Location = new Point(250, 200);
            btnCalculate.Size = new Size(180, 35);
            btnCalculate.BackColor = Color.LightBlue;
            btnCalculate.Click += new EventHandler(BtnCalculate_Click);
            this.Controls.Add(btnCalculate);

            // النتيجة
            Label lblResult = new Label();
            lblResult.Text = "إجمالي غرامة التأخير:";
            lblResult.Location = new Point(450, 250);
            lblResult.AutoSize = true;
            lblResult.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.Controls.Add(lblResult);

            txtResult = new TextBox();
            txtResult.Location = new Point(250, 250);
            txtResult.Size = new Size(180, 25);
            txtResult.ReadOnly = true;
            txtResult.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            txtResult.BackColor = Color.LightYellow;
            this.Controls.Add(txtResult);

            // زر التلميحات
            Button btnHelp = new Button();
            btnHelp.Text = "تلميحات";
            btnHelp.Location = new Point(450, 200);
            btnHelp.Size = new Size(80, 35);
            btnHelp.BackColor = Color.LightGreen;
            btnHelp.Click += new EventHandler(BtnHelp_Click);
            this.Controls.Add(btnHelp);

            // ملاحظة قانونية
            Label lblLegalNote = new Label();
            lblLegalNote.Text = "ملاحظة: إجمالي الغرامة لا يتجاوز أصل المبلغ طبقاً لنص المادة 121 من قانون التأمينات";
            lblLegalNote.Location = new Point(50, 290);
            lblLegalNote.Size = new Size(500, 30);
            lblLegalNote.Font = new Font("Segoe UI", 9F, FontStyle.Italic);
            lblLegalNote.ForeColor = Color.DarkRed;
            this.Controls.Add(lblLegalNote);
        }

        private void BtnCalculate_Click(object sender, EventArgs e)
        {
            try
            {
                decimal installmentValue, delayMonths, interestRate;

                if (decimal.TryParse(txtInstallmentValue.Text, out installmentValue) &&
                    decimal.TryParse(txtDelayMonths.Text, out delayMonths) &&
                    decimal.TryParse(txtInterestRate.Text, out interestRate))
                {
                    // تطبيق المعادلة: قيمة القسط × عدد الشهور × نسبة الفائدة ÷ 12 × عدد الشهور ÷ 2
                    decimal delayInterest = Math.Round(
                        installmentValue * delayMonths * (interestRate / 100m) / 12m * delayMonths / 2m,
                        2
                    );

                    // تطبيق المادة 121: الغرامة لا تتجاوز أصل المبلغ
                    if (delayInterest > installmentValue)
                    {
                        delayInterest = installmentValue;
                        txtResult.Text = delayInterest.ToString("N2") + " جنيه (محدود بأصل المبلغ)";
                    }
                    else
                    {
                        txtResult.Text = delayInterest.ToString("N2") + " جنيه";
                    }
                }
                else
                {
                    MessageBox.Show("الرجاء إدخال قيم صحيحة في جميع الحقول", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("حدث خطأ: {0}", ex.Message), "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnHelp_Click(object sender, EventArgs e)
        {
            string helpMessage = "المعادلة المستخدمة:\n" +
                               "قيمة القسط × عدد الشهور × نسبة الفائدة ÷ 12 × عدد الشهور ÷ 2\n\n" +
                               "مثال توضيحي:\n" +
                               "29 × 10 شهور × 15.50% ÷ 12 × 10 شهور ÷ 2 = 18.72 جنيه\n\n" +
                               "ملاحظة قانونية:\n" +
                               "طبقاً للمادة 121 من قانون التأمينات، لا يجوز أن تتجاوز إجمالي الغرامة أصل المبلغ المستحق.";

            MessageBox.Show(helpMessage, "تلميحات وشرح المعادلة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
